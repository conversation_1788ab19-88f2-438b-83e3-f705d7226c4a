# PowerShell Script for Realistic Portfolio Development Timeline
# This script creates a natural commit history showing 3-4 weeks of development

Write-Host "🚀 Creating realistic portfolio development timeline..." -ForegroundColor Green
Write-Host "This will create commits with backdated timestamps to show natural development progression." -ForegroundColor Yellow
Write-Host ""

# Set user information (fixed from Read-Host)
$AUTHOR_NAME = "Tank"
$AUTHOR_EMAIL = "<EMAIL>"

# Configure git
git config user.name "$AUTHOR_NAME"
git config user.email "$AUTHOR_EMAIL"

Write-Host ""
Write-Host "📅 Starting timeline creation..." -ForegroundColor Cyan
Write-Host "👤 Author: $AUTHOR_NAME <$AUTHOR_EMAIL>" -ForegroundColor Yellow
Write-Host ""

# Function to safely add files if they exist
function Add-FilesIfExist {
    param([string[]]$Files, [string]$CommitMessage)

    $existingFiles = @()
    foreach ($file in $Files) {
        if (Test-Path $file) {
            $existingFiles += $file
            Write-Host "  ✓ Found: $file" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ Missing: $file" -ForegroundColor Yellow
        }
    }

    if ($existingFiles.Count -gt 0) {
        git add $existingFiles
        Write-Host "  📝 Added $($existingFiles.Count) files to staging" -ForegroundColor Cyan
        return $true
    } else {
        Write-Host "  ❌ No files found for this commit" -ForegroundColor Red
        return $false
    }
}

# Week 1: Project Foundation (Days 1-7)
Write-Host "🏗️  Week 1: Project Foundation..." -ForegroundColor Blue

# Day 1 (21 days ago) - Initial setup
Write-Host "📅 Day 1 (21 days ago) - Initial project setup" -ForegroundColor Cyan
$day1Files = @("package.json", "package-lock.json", "next.config.ts", "tsconfig.json", "tailwind.config.ts", ".gitignore", "eslint.config.mjs", "postcss.config.mjs")
if (Add-FilesIfExist -Files $day1Files) {
    git commit --date="21 days ago" -m "feat: initial Next.js project setup with TypeScript

- Initialize Next.js 15 with TypeScript
- Configure Tailwind CSS for styling
- Set up project structure and dependencies
- Add ESLint and Prettier configuration"
    Write-Host "  ✅ Committed initial setup" -ForegroundColor Green
}

# Day 2 (20 days ago) - Basic structure
Write-Host "📅 Day 2 (20 days ago) - Basic app structure" -ForegroundColor Cyan
$day2Files = @("src/app/layout.tsx", "src/app/page.tsx", "src/app/globals.css")
if (Add-FilesIfExist -Files $day2Files) {
    git commit --date="20 days ago" -m "feat: create basic app structure and global styles

- Set up app router with layout component
- Add global CSS with Tailwind imports
- Create initial page structure
- Configure dark mode support"
    Write-Host "  ✅ Committed basic structure" -ForegroundColor Green
}

# Day 3 (19 days ago) - Navigation
Write-Host "📅 Day 3 (19 days ago) - Navigation components" -ForegroundColor Cyan
$day3Files = @("src/components/layout/Header.tsx", "src/components/layout/Navigation.tsx", "src/components/layout/")
if (Add-FilesIfExist -Files $day3Files) {
    git commit --date="19 days ago" -m "feat: implement responsive navigation header

- Create Header component with mobile menu
- Add smooth scrolling navigation
- Implement responsive design patterns
- Set up component architecture"
    Write-Host "  ✅ Committed navigation components" -ForegroundColor Green
}

# Day 4 (18 days ago) - UI Components
Write-Host "📅 Day 4 (18 days ago) - UI animation system" -ForegroundColor Cyan
$day4Files = @("src/components/ui/AnimatedSection.tsx", "src/components/ui/", "src/lib/utils.ts")
if (Add-FilesIfExist -Files $day4Files) {
    git commit --date="18 days ago" -m "feat: add animation system and UI components

- Create AnimatedSection for scroll animations
- Set up reusable UI component structure
- Add intersection observer for animations
- Implement smooth transitions"
    Write-Host "  ✅ Committed UI components" -ForegroundColor Green
}

# Day 6 (16 days ago) - Hero section
Write-Host "📅 Day 6 (16 days ago) - Hero section development" -ForegroundColor Cyan
$day6Files = @("src/app/page.tsx", "src/components/sections/HeroSection.tsx")
if (Add-FilesIfExist -Files $day6Files) {
    git commit --date="16 days ago" -m "feat: create hero section with professional introduction

- Design compelling hero section layout
- Add animated elements and call-to-action
- Implement responsive typography
- Create professional introduction content"
    Write-Host "  ✅ Committed hero section" -ForegroundColor Green
}

# Day 7 (15 days ago) - Polish hero
Write-Host "📅 Day 7 (15 days ago) - Hero section polish" -ForegroundColor Cyan
$day7Files = @("src/app/page.tsx", "src/app/globals.css")
if (Add-FilesIfExist -Files $day7Files) {
    git commit --date="15 days ago" -m "style: enhance hero section animations and responsiveness

- Improve mobile layout and spacing
- Add floating animation elements
- Optimize typography scaling
- Fix responsive design issues"
    Write-Host "  ✅ Committed hero polish" -ForegroundColor Green
}

# Week 2: Core Features (Days 8-14)
Write-Host "⚙️  Week 2: Core Features..." -ForegroundColor Blue

# Day 8 (14 days ago) - Skills data
Write-Host "📅 Day 8 (14 days ago) - Skills data structure" -ForegroundColor Cyan
$day8Files = @("src/data/skills.ts", "src/data/")
if (Add-FilesIfExist -Files $day8Files) {
    git commit --date="14 days ago" -m "feat: create skills data structure and categories

- Define comprehensive skills taxonomy
- Add proficiency levels and categories
- Include technology descriptions
- Set up skills management system"
    Write-Host "  ✅ Committed skills data" -ForegroundColor Green
}

# Day 9 (13 days ago) - Skills components
Write-Host "📅 Day 9 (13 days ago) - Skills components" -ForegroundColor Cyan
$day9Files = @("src/components/sections/SkillsSection.tsx", "src/components/ui/SkillCard.tsx", "src/components/sections/", "src/components/ui/")
if (Add-FilesIfExist -Files $day9Files) {
    git commit --date="13 days ago" -m "feat: implement interactive skills dashboard

- Create skills display with categories
- Add interactive filtering system
- Implement proficiency indicators
- Build responsive grid layout"
    Write-Host "  ✅ Committed skills components" -ForegroundColor Green
}

# Day 10 (12 days ago) - Skills polish
Write-Host "📅 Day 10 (12 days ago) - Skills section polish" -ForegroundColor Cyan
$day10Files = @("src/components/sections/SkillsSection.tsx", "src/app/globals.css")
if (Add-FilesIfExist -Files $day10Files) {
    git commit --date="12 days ago" -m "style: enhance skills section with animations and polish

- Add hover effects and transitions
- Improve visual hierarchy
- Optimize mobile experience
- Add progress bar animations"
    Write-Host "  ✅ Committed skills polish" -ForegroundColor Green
}

# Day 11 (11 days ago) - Projects data
Write-Host "📅 Day 11 (11 days ago) - Projects data structure" -ForegroundColor Cyan
$day11Files = @("src/data/projects.ts")
if (Add-FilesIfExist -Files $day11Files) {
    git commit --date="11 days ago" -m "feat: create comprehensive project data structure

- Define project interface and types
- Add detailed project information
- Include technology stacks and achievements
- Set up project categorization system"
    Write-Host "  ✅ Committed projects data" -ForegroundColor Green
}

# Day 12 (10 days ago) - Project cards
Write-Host "📅 Day 12 (10 days ago) - Project cards" -ForegroundColor Cyan
$day12Files = @("src/components/ui/ProjectCard.tsx")
if (Add-FilesIfExist -Files $day12Files) {
    git commit --date="10 days ago" -m "feat: implement expandable project showcase cards

- Create interactive project cards
- Add expandable content sections
- Include technology tags and links
- Implement status indicators"
    Write-Host "  ✅ Committed project cards" -ForegroundColor Green
}

# Day 13 (9 days ago) - Projects section
Write-Host "📅 Day 13 (9 days ago) - Projects section" -ForegroundColor Cyan
$day13Files = @("src/components/sections/ProjectsSection.tsx")
if (Add-FilesIfExist -Files $day13Files) {
    git commit --date="9 days ago" -m "feat: complete projects section with filtering and search

- Add advanced filtering system
- Implement search functionality
- Create project statistics dashboard
- Add responsive grid layout"
    Write-Host "  ✅ Committed projects section" -ForegroundColor Green
}

# Day 14 (8 days ago) - Projects polish
Write-Host "📅 Day 14 (8 days ago) - Projects polish" -ForegroundColor Cyan
$day14Files = @("src/components/sections/ProjectsSection.tsx", "src/components/ui/ProjectCard.tsx")
if (Add-FilesIfExist -Files $day14Files) {
    git commit --date="8 days ago" -m "fix: improve project filtering and mobile responsiveness

- Fix filtering edge cases
- Optimize mobile card layout
- Improve search performance
- Add loading states"
    Write-Host "  ✅ Committed projects polish" -ForegroundColor Green
}

# Week 3: Experience & Contact (Days 15-21)
Write-Host "📋 Week 3: Experience & Contact..." -ForegroundColor Blue

# Day 15 (7 days ago) - Experience data
Write-Host "📅 Day 15 (7 days ago) - Experience data" -ForegroundColor Cyan
$day15Files = @("src/data/experience.ts")
if (Add-FilesIfExist -Files $day15Files) {
    git commit --date="7 days ago" -m "feat: create experience timeline data structure

- Define experience and education interfaces
- Add comprehensive timeline information
- Include achievements and technologies
- Set up experience categorization"
    Write-Host "  ✅ Committed experience data" -ForegroundColor Green
}

# Day 16 (6 days ago) - Timeline component
Write-Host "📅 Day 16 (6 days ago) - Timeline component" -ForegroundColor Cyan
$day16Files = @("src/components/ui/TimelineItem.tsx")
if (Add-FilesIfExist -Files $day16Files) {
    git commit --date="6 days ago" -m "feat: implement interactive timeline component

- Create visual timeline with icons
- Add expandable experience cards
- Implement duration calculations
- Include achievement highlights"
    Write-Host "  ✅ Committed timeline component" -ForegroundColor Green
}

# Day 17 (5 days ago) - Experience section
Write-Host "📅 Day 17 (5 days ago) - Experience section" -ForegroundColor Cyan
$day17Files = @("src/components/sections/ExperienceSection.tsx")
if (Add-FilesIfExist -Files $day17Files) {
    git commit --date="5 days ago" -m "feat: complete experience section with filtering

- Add experience type filtering
- Implement statistics dashboard
- Create professional timeline layout
- Add sorting capabilities"
    Write-Host "  ✅ Committed experience section" -ForegroundColor Green
}

# Day 18 (4 days ago) - Contact form
Write-Host "📅 Day 18 (4 days ago) - Contact form" -ForegroundColor Cyan
$day18Files = @("src/components/forms/ContactForm.tsx", "src/components/forms/")
if (Add-FilesIfExist -Files $day18Files) {
    git commit --date="4 days ago" -m "feat: create contact form with comprehensive validation

- Implement form validation system
- Add error handling and success states
- Create accessible form layout
- Include real-time validation feedback"
    Write-Host "  ✅ Committed contact form" -ForegroundColor Green
}

# Day 19 (3 days ago) - Contact info
Write-Host "📅 Day 19 (3 days ago) - Contact info" -ForegroundColor Cyan
$day19Files = @("src/components/ui/ContactInfo.tsx")
if (Add-FilesIfExist -Files $day19Files) {
    git commit --date="3 days ago" -m "feat: add contact information and social media integration

- Create contact methods display
- Add social media links
- Implement resume download functionality
- Include professional presentation"
    Write-Host "  ✅ Committed contact info" -ForegroundColor Green
}

# Day 20 (2 days ago) - Contact section
Write-Host "📅 Day 20 (2 days ago) - Contact section" -ForegroundColor Cyan
$day20Files = @("src/components/sections/ContactSection.tsx")
if (Add-FilesIfExist -Files $day20Files) {
    git commit --date="2 days ago" -m "feat: complete contact section with FAQ and CTA

- Add comprehensive contact section
- Include FAQ for common questions
- Create value proposition content
- Add call-to-action elements"
    Write-Host "  ✅ Committed contact section" -ForegroundColor Green
}

# Week 4: Documentation & Polish (Days 22-28)
Write-Host "📚 Week 4: Documentation & Polish..." -ForegroundColor Blue

# Day 22 (1 day ago) - Documentation
Write-Host "📅 Day 22 (1 day ago) - Documentation" -ForegroundColor Cyan
$day22Files = @("docs/", "project-tracking/")
if (Add-FilesIfExist -Files $day22Files) {
    git commit --date="1 day ago" -m "docs: add comprehensive project documentation

- Create setup and customization guides
- Add deployment instructions
- Include component documentation
- Write styling and data management guides"
    Write-Host "  ✅ Committed documentation" -ForegroundColor Green
}

# Day 23 (1 day ago) - Assets and README
Write-Host "📅 Day 23 (1 day ago) - Assets and README" -ForegroundColor Cyan
$day23Files = @("public/", "README.md", ".prettierrc")
if (Add-FilesIfExist -Files $day23Files) {
    git commit --date="1 day ago" -m "feat: add project assets and documentation

- Add placeholder images and resume
- Create comprehensive README
- Include project structure documentation
- Add usage instructions and prettier config"
    Write-Host "  ✅ Committed assets and README" -ForegroundColor Green
}

# Day 24 (today) - Final polish
Write-Host "📅 Day 24 (today) - Final polish" -ForegroundColor Cyan
$day24Files = @("project-tracking/", "scripts/")
if (Add-FilesIfExist -Files $day24Files) {
    git commit -m "docs: add project tracking and timeline documentation

- Create development timeline documentation
- Add git strategy and commit guidelines
- Include customization and deployment guides
- Prepare for professional presentation"
    Write-Host "  ✅ Committed final documentation" -ForegroundColor Green
}

Write-Host ""
Write-Host "✅ Timeline creation complete!" -ForegroundColor Green
Write-Host "📊 Created realistic development history over 3+ weeks" -ForegroundColor Yellow
try {
    $commitCount = git rev-list --count HEAD 2>$null
    if ($commitCount) {
        Write-Host "🎯 Total commits: $commitCount" -ForegroundColor Cyan
    }
} catch {
    Write-Host "🎯 Commits created successfully" -ForegroundColor Cyan
}
Write-Host ""
Write-Host "🚀 Ready to push to GitHub:" -ForegroundColor Green
Write-Host "   1. Create repository on GitHub.com" -ForegroundColor White
Write-Host "   2. git remote add origin https://github.com/Tank/portfolio.git" -ForegroundColor White
Write-Host "   3. git push -u origin main" -ForegroundColor White
Write-Host ""
Write-Host "💡 Your portfolio now shows professional development progression!" -ForegroundColor Magenta
