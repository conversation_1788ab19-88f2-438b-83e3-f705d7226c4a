{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"framer-motion": "^12.18.1", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-intersection-observer": "^9.16.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.3.4", "lucide-react": "^0.517.0", "prettier": "^3.5.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4", "typescript": "^5"}}