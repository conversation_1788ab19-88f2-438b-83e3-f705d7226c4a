import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Your Name - Portfolio | Master of Applied Computing Graduate",
  description: "<PERSON><PERSON><PERSON> of a Master of Applied Computing graduate from Lincoln University, New Zealand. Showcasing cloud technologies, data analytics, cybersecurity, and full-stack development projects.",
  keywords: ["portfolio", "software developer", "cloud computing", "data analytics", "cybersecurity", "full-stack development", "New Zealand", "Lincoln University"],
  authors: [{ name: "Your Name" }],
  creator: "Your Name",
  openGraph: {
    type: "website",
    locale: "en_NZ",
    url: "https://yourportfolio.vercel.app",
    title: "Your Name - Portfolio",
    description: "Master of Applied Computing graduate showcasing technical projects and skills",
    siteName: "Your Name Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Your Name - Portfolio",
    description: "Master of Applied Computing graduate showcasing technical projects and skills",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
