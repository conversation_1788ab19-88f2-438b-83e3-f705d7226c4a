# Fix GitHub Date Display Issue
# This script creates a fresh timeline with correct author AND committer dates

Write-Host "🚨 WARNING: This will delete git history and create fresh timeline!" -ForegroundColor Red
Write-Host "Press Ctrl+C to cancel, or press Enter to continue..."
Read-Host

# Remove git history
Write-Host "🗑️ Removing git history..." -ForegroundColor Yellow
Remove-Item -Recurse -Force ".git" -ErrorAction SilentlyContinue

# Initialize fresh repo
git init
git branch -M main
git config user.name "Tank"
git config user.email "<EMAIL>"

Write-Host "📅 Creating timeline with correct dates..." -ForegroundColor Cyan

# Day 1 (18 days ago) - June 1
Write-Host "Day 1: Initial setup" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-01 10:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-01 10:00:00"
git add package.json package-lock.json next.config.ts tsconfig.json .gitignore eslint.config.mjs postcss.config.mjs
git commit -m "feat: initial Next.js project setup with TypeScript

- Initialize Next.js 15 with TypeScript
- Configure Tailwind CSS for styling  
- Set up project structure and dependencies
- Add ESLint and Prettier configuration"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

# Day 2 (16 days ago) - June 3
Write-Host "Day 2: Basic structure" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-03 14:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-03 14:00:00"
git add src/app/layout.tsx src/app/page.tsx src/app/globals.css
git commit -m "feat: create basic app structure and global styles

- Set up app router with layout component
- Add global CSS with Tailwind imports
- Create initial page structure
- Configure dark mode support"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

# Day 3 (14 days ago) - June 5
Write-Host "Day 3: Navigation" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-05 11:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-05 11:00:00"
git add src/components/layout/
git commit -m "feat: implement responsive navigation header

- Create Header component with mobile menu
- Add smooth scrolling navigation
- Implement responsive design patterns
- Set up component architecture"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

# Day 4 (12 days ago) - June 7
Write-Host "Day 4: UI Components" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-07 15:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-07 15:00:00"
git add src/components/ui/ src/lib/utils.ts
git commit -m "feat: add animation system and UI components

- Create AnimatedSection for scroll animations
- Set up reusable UI component structure
- Add intersection observer for animations
- Implement smooth transitions"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

# Day 5 (10 days ago) - June 9
Write-Host "Day 5: Skills data" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-09 13:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-09 13:00:00"
git add src/data/
git commit -m "feat: create skills and project data structures

- Define comprehensive skills taxonomy
- Add project data with detailed information
- Include technology descriptions
- Set up data management system"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

# Day 6 (8 days ago) - June 11
Write-Host "Day 6: Skills & Projects sections" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-11 16:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-11 16:00:00"
git add src/components/sections/SkillsSection.tsx src/components/sections/ProjectsSection.tsx
git commit -m "feat: implement skills and projects sections

- Create interactive skills dashboard
- Add project showcase with filtering
- Implement responsive grid layouts
- Include search and filter functionality"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

# Day 7 (6 days ago) - June 13
Write-Host "Day 7: Experience section" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-13 12:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-13 12:00:00"
git add src/components/sections/ExperienceSection.tsx
git commit -m "feat: create experience timeline section

- Implement interactive timeline component
- Add experience filtering and statistics
- Create professional timeline layout
- Include achievement highlights"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

# Day 8 (4 days ago) - June 15
Write-Host "Day 8: Contact section" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-15 14:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-15 14:00:00"
git add src/components/forms/ src/components/sections/ContactSection.tsx
git commit -m "feat: complete contact section with form validation

- Create contact form with comprehensive validation
- Add contact information display
- Implement FAQ and call-to-action elements
- Include resume download functionality"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

# Day 9 (2 days ago) - June 17
Write-Host "Day 9: Documentation" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-17 10:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-17 10:00:00"
git add docs/ project-tracking/ README.md .prettierrc
git commit -m "docs: add comprehensive project documentation

- Create setup and customization guides
- Add deployment instructions and README
- Include component documentation
- Add project tracking and timeline strategy"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

# Day 10 (today) - June 19
Write-Host "Day 10: Final assets" -ForegroundColor Blue
$env:GIT_AUTHOR_DATE = "2025-06-19 09:00:00"
$env:GIT_COMMITTER_DATE = "2025-06-19 09:00:00"
git add public/ scripts/
git commit -m "feat: add project assets and development scripts

- Add placeholder images and resume
- Include git timeline and development scripts
- Prepare for professional presentation
- Complete portfolio development cycle"
Remove-Item env:GIT_AUTHOR_DATE -ErrorAction SilentlyContinue
Remove-Item env:GIT_COMMITTER_DATE -ErrorAction SilentlyContinue

Write-Host ""
Write-Host "✅ Fresh timeline created with correct dates!" -ForegroundColor Green
Write-Host "📊 Timeline spans 2.5 weeks (June 1-19, 2025)" -ForegroundColor Yellow

$commitCount = git rev-list --count HEAD
Write-Host "🎯 Total commits: $commitCount" -ForegroundColor Cyan

Write-Host ""
Write-Host "🚀 Next steps:" -ForegroundColor Green
Write-Host "   1. Create new GitHub repository: https://github.com/new" -ForegroundColor White
Write-Host "   2. Name it 'portfolio'" -ForegroundColor White
Write-Host "   3. git remote add origin https://github.com/TankHao12/portfolio.git" -ForegroundColor White
Write-Host "   4. git push -u origin main" -ForegroundColor White
Write-Host ""
Write-Host "💡 GitHub will now show the correct development dates!" -ForegroundColor Magenta
